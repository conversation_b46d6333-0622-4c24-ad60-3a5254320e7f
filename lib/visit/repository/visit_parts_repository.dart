import 'package:serwis_app/core/repository.dart';
import 'package:serwis_app/visit/models/visit_part.dart';

class VisitPartsRepository extends AuthenticatedRepository {
  VisitPartsRepository({
    required super.token,
  });

  Future<List<VisitPart>> getVisitParts(int visitId) async {
    final response = await api.get('/api/visit/$visitId/parts');
    final data = response.data as List<dynamic>;
    // final parts = data.map((e) => VisitPart.fromJson(e)).toList();

    return [];
  }
}
