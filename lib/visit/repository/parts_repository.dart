import 'package:serwis_app/core/repository.dart';

class Part {}

class PartsRepository extends AuthenticatedRepository {
  PartsRepository({
    required super.token,
  });

  Future<List<Part>> getParts() async {
    final response = await api.get('/api/parts');
    final data = response.data as List<dynamic>;
    // final parts = data.map((e) => Part.fromJson(e)).toList();

    // return parts;
    return [];
  }
}
