import 'package:hydrated_bloc/hydrated_bloc.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/visit/models/visit_part.dart';
import 'package:serwis_app/visit/repository/parts_repository.dart';
import 'package:serwis_app/visit/repository/visit_parts_repository.dart';

enum VisitPartsStatus {
  loading,
  ready,
}

class VisitPartsState {
  final VisitPartsStatus status;
  final List<VisitPart> visitParts;
  final List<Part> servicemanParts;
  final dynamic error;

  const VisitPartsState({
    required this.status,
    required this.visitParts,
    required this.servicemanParts,
    this.error,
  });

  factory VisitPartsState.initial() {
    return const VisitPartsState(
      status: VisitPartsStatus.loading,
      visitParts: [],
      servicemanParts: [],
    );
  }

  VisitPartsState copyWith({
    VisitPartsStatus? status,
    List<VisitPart>? visitParts,
    List<Part>? servicemanParts,
    dynamic error,
  }) {
    return VisitPartsState(
      status: status ?? this.status,
      visitParts: visitParts ?? this.visitParts,
      servicemanParts: servicemanParts ?? this.servicemanParts,
      error: error,
    );
  }
}

class VisitPartsCubit extends Cubit<VisitPartsState> {
  final VisitPartsRepository visitPartsRepository;
  final PartsRepository partsRepository;
  final Visit visit;

  VisitPartsCubit({
    required this.visitPartsRepository,
    required this.partsRepository,
    required this.visit,
  }) : super(VisitPartsState.initial());

  Future<void> init() async {
    emit(state.copyWith(status: VisitPartsStatus.loading));
    try {
      final data = await Future.wait([
        partsRepository.getParts(),
        visitPartsRepository.getVisitParts(visit.id),
      ]);

      emit(state.copyWith(
        status: VisitPartsStatus.ready,
        servicemanParts: data[0] as List<Part>,
        visitParts: data[1] as List<VisitPart>,
      ));
    } catch (e) {
      emit(state.copyWith(status: VisitPartsStatus.ready, error: e));
    }
  }

  Future<void> getVisitParts() async {
    try {
      emit(state.copyWith(status: VisitPartsStatus.loading));

      final parts = await visitPartsRepository.getVisitParts(visit.id);

      emit(state.copyWith(status: VisitPartsStatus.ready, visitParts: parts));
    } catch (e) {
      emit(state.copyWith(status: VisitPartsStatus.ready, error: e));
    }
  }
}
