import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/visit/cubit/visit_parts_cubit.dart';

class VisitPartsScreen extends StatelessWidget {
  const VisitPartsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final visit = context.read<VisitPartsCubit>().visit;

    return Scaffold(
      appBar: AppBar(
        title: const Text('<PERSON><PERSON><PERSON><PERSON><PERSON>'),
      ),
      body: const Center(
        child: Text('<PERSON><PERSON><PERSON><PERSON><PERSON>'),
      ),
    );
  }
}
